<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\DriverMineAssigment;
use Barryvdh\DomPDF\Facade\Pdf;

class MineAsigmentReportController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request, $id)
    {
        $record = DriverMineAssigment::with(['driver', 'mine'])
            ->where('mine_id', $id)
            ->whereMonth('created_at', $request->month)
            ->whereYear('created_at', $request->year)
            ->get();
        $pdf = Pdf::loadView('pdf.assigment-mine-driver-report', [
            'record' => $record,
        ]);

        return $pdf->stream();
    }
}
